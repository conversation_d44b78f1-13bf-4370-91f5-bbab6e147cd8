<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>API Endpoints Test</h1>
    
    <div class="test-section">
        <h3>Test Base Pricing API</h3>
        <button onclick="testBasePricing()">Test Base Pricing</button>
        <div id="base-pricing-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test Public Base Pricing API</h3>
        <button onclick="testPublicBasePricing()">Test Public Base Pricing</button>
        <div id="public-pricing-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test PC Orders API</h3>
        <button onclick="testPCOrders()">Test PC Orders</button>
        <div id="pc-orders-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Setup Base Pricing Table</h3>
        <button onclick="setupTable()">Setup Table</button>
        <div id="setup-result" class="result"></div>
    </div>

    <script>
        function testBasePricing() {
            fetch('pc_base_pricing_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_base_pricing',
                credentials: 'same-origin'
            })
            .then(response => response.text())
            .then(text => {
                document.getElementById('base-pricing-result').innerHTML = '<pre>' + text + '</pre>';
                try {
                    const data = JSON.parse(text);
                    document.getElementById('base-pricing-result').className = 'result success';
                } catch (e) {
                    document.getElementById('base-pricing-result').className = 'result error';
                }
            })
            .catch(error => {
                document.getElementById('base-pricing-result').innerHTML = 'Error: ' + error.message;
                document.getElementById('base-pricing-result').className = 'result error';
            });
        }

        function testPublicBasePricing() {
            fetch('pc_base_pricing_public_api.php', {
                credentials: 'same-origin'
            })
            .then(response => response.text())
            .then(text => {
                document.getElementById('public-pricing-result').innerHTML = '<pre>' + text + '</pre>';
                try {
                    const data = JSON.parse(text);
                    document.getElementById('public-pricing-result').className = 'result success';
                } catch (e) {
                    document.getElementById('public-pricing-result').className = 'result error';
                }
            })
            .catch(error => {
                document.getElementById('public-pricing-result').innerHTML = 'Error: ' + error.message;
                document.getElementById('public-pricing-result').className = 'result error';
            });
        }

        function testPCOrders() {
            fetch('pc_components_api.php?action=admin_get_pc_orders', {
                credentials: 'same-origin'
            })
            .then(response => response.text())
            .then(text => {
                document.getElementById('pc-orders-result').innerHTML = '<pre>' + text + '</pre>';
                try {
                    const data = JSON.parse(text);
                    document.getElementById('pc-orders-result').className = 'result success';
                } catch (e) {
                    document.getElementById('pc-orders-result').className = 'result error';
                }
            })
            .catch(error => {
                document.getElementById('pc-orders-result').innerHTML = 'Error: ' + error.message;
                document.getElementById('pc-orders-result').className = 'result error';
            });
        }

        function setupTable() {
            fetch('setup_base_pricing_table.php', {
                credentials: 'same-origin'
            })
            .then(response => response.text())
            .then(text => {
                document.getElementById('setup-result').innerHTML = '<pre>' + text + '</pre>';
                try {
                    const data = JSON.parse(text);
                    document.getElementById('setup-result').className = 'result success';
                } catch (e) {
                    document.getElementById('setup-result').className = 'result error';
                }
            })
            .catch(error => {
                document.getElementById('setup-result').innerHTML = 'Error: ' + error.message;
                document.getElementById('setup-result').className = 'result error';
            });
        }
    </script>
</body>
</html>
