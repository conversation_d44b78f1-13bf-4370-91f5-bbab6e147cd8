<?php
require_once 'config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<h1>Database Debug Information</h1>";

// Test database connection
echo "<h2>Database Connection Test</h2>";
$local_link = get_db_connection();

if ($local_link) {
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Check if pc_base_pricing table exists
    echo "<h2>Table Existence Check</h2>";
    $sql = "SHOW TABLES LIKE 'pc_base_pricing'";
    $result = mysqli_query($local_link, $sql);
    
    if (mysqli_num_rows($result) > 0) {
        echo "<p style='color: green;'>✅ Table 'pc_base_pricing' exists</p>";
        
        // Check table structure
        echo "<h3>Table Structure</h3>";
        $sql = "DESCRIBE pc_base_pricing";
        $result = mysqli_query($local_link, $sql);
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($row = mysqli_fetch_assoc($result)) {
            echo "<tr>";
            echo "<td>{$row['Field']}</td>";
            echo "<td>{$row['Type']}</td>";
            echo "<td>{$row['Null']}</td>";
            echo "<td>{$row['Key']}</td>";
            echo "<td>{$row['Default']}</td>";
            echo "<td>{$row['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check data
        echo "<h3>Table Data</h3>";
        $sql = "SELECT * FROM pc_base_pricing";
        $result = mysqli_query($local_link, $sql);
        
        if (mysqli_num_rows($result) > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Type</th><th>Key</th><th>Price</th><th>Description</th><th>Active</th></tr>";
            while ($row = mysqli_fetch_assoc($result)) {
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['pricing_type']}</td>";
                echo "<td>{$row['config_key']}</td>";
                echo "<td>\${$row['price']}</td>";
                echo "<td>" . substr($row['description'], 0, 50) . "...</td>";
                echo "<td>" . ($row['is_active'] ? 'Yes' : 'No') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ Table exists but has no data</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Table 'pc_base_pricing' does not exist</p>";
    }
    
    // Check if pc_orders table exists
    echo "<h2>PC Orders Table Check</h2>";
    $sql = "SHOW TABLES LIKE 'pc_orders'";
    $result = mysqli_query($local_link, $sql);
    
    if (mysqli_num_rows($result) > 0) {
        echo "<p style='color: green;'>✅ Table 'pc_orders' exists</p>";
        
        $sql = "SELECT COUNT(*) as count FROM pc_orders";
        $result = mysqli_query($local_link, $sql);
        $row = mysqli_fetch_assoc($result);
        echo "<p>Number of PC orders: {$row['count']}</p>";
    } else {
        echo "<p style='color: red;'>❌ Table 'pc_orders' does not exist</p>";
    }
    
    close_db_connection($local_link);
} else {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
}

echo "<h2>Quick Setup</h2>";
echo "<p><a href='setup_base_pricing_table.php' target='_blank'>Setup Base Pricing Table</a></p>";
echo "<p><a href='test_api_endpoints.php' target='_blank'>Test API Endpoints</a></p>";
?>
