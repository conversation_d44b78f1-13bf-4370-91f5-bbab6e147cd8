<?php
// PC Components Management API
session_start();
require_once 'config.php';
require_once 'language.php';

// Set content type to JSON with UTF-8 encoding
header('Content-Type: application/json; charset=utf-8');

// CORS headers for development
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Check admin access
function checkAdminAccess() {
    if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
        echo json_encode(['success' => false, 'message' => 'Admin access required']);
        exit();
    }
}

// Check user access
function checkUserAccess() {
    if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
        echo json_encode(['success' => false, 'message' => 'User login required']);
        exit();
    }
}

// execute_query function is now defined in config.php

// Handle different API actions
$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'get_categories':
        getCategories();
        break;
    case 'get_components':
        getComponents();
        break;
    case 'get_components_by_category':
        getComponentsByCategory();
        break;
    case 'get_component':
        getComponent();
        break;
    case 'add_component':
        checkAdminAccess();
        addComponent();
        break;
    case 'update_component':
        checkAdminAccess();
        updateComponent();
        break;
    case 'delete_component':
        checkAdminAccess();
        deleteComponent();
        break;
    case 'get_prebuilt_configs':
        getPrebuiltConfigs();
        break;
    case 'get_prebuilt_config':
        getPrebuiltConfig();
        break;
    case 'add_prebuilt_config':
        checkAdminAccess();
        addPrebuiltConfig();
        break;
    case 'update_prebuilt_config':
        checkAdminAccess();
        updatePrebuiltConfig();
        break;
    case 'delete_prebuilt_config':
        checkAdminAccess();
        deletePrebuiltConfig();
        break;
    case 'create_pc_order':
        checkUserAccess();
        createPCOrder();
        break;
    case 'get_pc_orders':
        checkUserAccess();
        getPCOrders();
        break;
    case 'admin_get_pc_orders':
        checkAdminAccess();
        adminGetPCOrders();
        break;
    case 'admin_update_pc_order':
        checkAdminAccess();
        adminUpdatePCOrder();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
}

/**
 * Get all component categories
 */
function getCategories() {
    $local_link = get_db_connection();

    // Check if table exists first
    $tableCheck = mysqli_query($local_link, "SHOW TABLES LIKE 'pc_component_categories'");
    if (mysqli_num_rows($tableCheck) == 0) {
        echo json_encode(['success' => false, 'message' => 'PC components system not initialized. Please contact administrator.']);
        close_db_connection($local_link);
        return;
    }

    $sql = "SELECT * FROM pc_component_categories WHERE is_active = 1 ORDER BY sort_order, category_name";
    $result = mysqli_query($local_link, $sql);

    if ($result) {
        $categories = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $categories[] = $row;
        }
        echo json_encode(['success' => true, 'categories' => $categories]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch categories: ' . mysqli_error($local_link)]);
    }

    close_db_connection($local_link);
}

/**
 * Get components by category
 */
function getComponents() {
    $local_link = get_db_connection();
    
    $category_id = $_GET['category_id'] ?? null;
    $active_only = $_GET['active_only'] ?? true;
    
    $sql = "SELECT c.*, cat.category_name, cat.category_name_en, cat.category_name_zh 
            FROM pc_components c 
            JOIN pc_component_categories cat ON c.category_id = cat.id";
    
    $params = [];
    $types = "";
    
    if ($category_id) {
        $sql .= " WHERE c.category_id = ?";
        $params[] = $category_id;
        $types .= "i";
        
        if ($active_only) {
            $sql .= " AND c.is_active = 1";
        }
    } else if ($active_only) {
        $sql .= " WHERE c.is_active = 1";
    }
    
    $sql .= " ORDER BY c.sort_order, c.component_name";
    
    $stmt = execute_query($local_link, $sql, $types, $params);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $components = [];
        while ($row = mysqli_fetch_assoc($result)) {
            // Decode JSON specifications
            if ($row['specifications']) {
                $row['specifications'] = json_decode($row['specifications'], true);
            }
            $components[] = $row;
        }
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'components' => $components]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch components']);
    }
    
    close_db_connection($local_link);
}

/**
 * Get components grouped by category
 */
function getComponentsByCategory() {
    $local_link = get_db_connection();

    $active_only = $_GET['active_only'] ?? 1;

    $sql = "SELECT c.*, cat.category_name
            FROM pc_components c
            JOIN pc_component_categories cat ON c.category_id = cat.id
            WHERE cat.is_active = 1";

    if ($active_only) {
        $sql .= " AND c.is_active = 1";
    }

    $sql .= " ORDER BY cat.sort_order, c.sort_order, c.component_name";

    $result = mysqli_query($local_link, $sql);

    if ($result) {
        $components_by_category = [];

        while ($row = mysqli_fetch_assoc($result)) {
            // Decode JSON specifications
            if ($row['specifications']) {
                $row['specifications'] = json_decode($row['specifications'], true);
            }

            $category = strtolower($row['category_name']);
            if (!isset($components_by_category[$category])) {
                $components_by_category[$category] = [];
            }

            $components_by_category[$category][] = $row;
        }

        echo json_encode([
            'success' => true,
            'components_by_category' => $components_by_category
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch components']);
    }

    close_db_connection($local_link);
}

/**
 * Get single component
 */
function getComponent() {
    $local_link = get_db_connection();
    
    $id = $_GET['id'] ?? 0;
    if (!$id) {
        echo json_encode(['success' => false, 'message' => 'Component ID required']);
        close_db_connection($local_link);
        return;
    }
    
    $sql = "SELECT c.*, cat.category_name, cat.category_name_en, cat.category_name_zh 
            FROM pc_components c 
            JOIN pc_component_categories cat ON c.category_id = cat.id 
            WHERE c.id = ?";
    
    $stmt = execute_query($local_link, $sql, "i", [$id]);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        if ($row = mysqli_fetch_assoc($result)) {
            // Decode JSON specifications
            if ($row['specifications']) {
                $row['specifications'] = json_decode($row['specifications'], true);
            }
            // Debug log for description
            error_log("Get component - Description from DB: " . ($row['description'] ?? 'NULL'));
            echo json_encode(['success' => true, 'component' => $row], JSON_UNESCAPED_UNICODE);
        } else {
            echo json_encode(['success' => false, 'message' => 'Component not found']);
        }
        mysqli_stmt_close($stmt);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch component']);
    }
    
    close_db_connection($local_link);
}

/**
 * Add new component
 */
function addComponent() {
    $local_link = get_db_connection();
    
    $category_id = $_POST['category_id'] ?? 0;
    $component_name = $_POST['component_name'] ?? '';
    $component_name_en = $_POST['component_name_en'] ?? $component_name;
    $component_name_zh = $_POST['component_name_zh'] ?? $component_name;
    $brand = $_POST['brand'] ?? '';
    $model = $_POST['model'] ?? '';
    $specifications = $_POST['specifications'] ?? '{}';
    $base_price = floatval($_POST['base_price'] ?? 0);
    $current_price = floatval($_POST['current_price'] ?? $base_price);
    $stock_quantity = intval($_POST['stock_quantity'] ?? 0);
    $description = trim($_POST['description'] ?? '');
    $description_en = trim($_POST['description_en'] ?? $description);
    $description_zh = trim($_POST['description_zh'] ?? $description);

    // Ensure proper UTF-8 encoding
    $description = mb_convert_encoding($description, 'UTF-8', 'UTF-8');
    $description_en = mb_convert_encoding($description_en, 'UTF-8', 'UTF-8');
    $description_zh = mb_convert_encoding($description_zh, 'UTF-8', 'UTF-8');
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    $sort_order = intval($_POST['sort_order'] ?? 0);
    
    if (!$category_id || !$component_name || $base_price < 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid input data']);
        close_db_connection($local_link);
        return;
    }
    
    // Validate JSON specifications
    if (!json_decode($specifications)) {
        $specifications = '{}';
    }
    
    $sql = "INSERT INTO pc_components 
            (category_id, component_name, component_name_en, component_name_zh, brand, model, 
             specifications, base_price, current_price, stock_quantity, description, 
             description_en, description_zh, is_active, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = execute_query($local_link, $sql, "issssssddiisssi", [
        $category_id, $component_name, $component_name_en, $component_name_zh, $brand, $model,
        $specifications, $base_price, $current_price, $stock_quantity, $description,
        $description_en, $description_zh, $is_active, $sort_order
    ]);
    
    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'Component added successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to add component']);
    }
    
    close_db_connection($local_link);
}

/**
 * Update component
 */
function updateComponent() {
    $local_link = get_db_connection();

    $id = $_POST['id'] ?? 0;
    if (!$id) {
        echo json_encode(['success' => false, 'message' => 'Component ID required']);
        close_db_connection($local_link);
        return;
    }

    $category_id = $_POST['category_id'] ?? 0;
    $component_name = $_POST['component_name'] ?? '';
    $component_name_en = $_POST['component_name_en'] ?? '';
    $component_name_zh = $_POST['component_name_zh'] ?? '';
    $brand = $_POST['brand'] ?? '';
    $model = $_POST['model'] ?? '';
    $specifications = $_POST['specifications'] ?? '{}';
    $base_price = floatval($_POST['base_price'] ?? 0);
    $current_price = floatval($_POST['current_price'] ?? 0);
    $stock_quantity = intval($_POST['stock_quantity'] ?? 0);
    $description = trim($_POST['description'] ?? '');
    $description_en = trim($_POST['description_en'] ?? '');
    $description_zh = trim($_POST['description_zh'] ?? '');

    // Ensure proper UTF-8 encoding
    $description = mb_convert_encoding($description, 'UTF-8', 'UTF-8');
    $description_en = mb_convert_encoding($description_en, 'UTF-8', 'UTF-8');
    $description_zh = mb_convert_encoding($description_zh, 'UTF-8', 'UTF-8');

    // Debug log
    error_log("Update component - Description received: " . $description);
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    $sort_order = intval($_POST['sort_order'] ?? 0);

    // Validate JSON specifications
    if (!json_decode($specifications)) {
        $specifications = '{}';
    }

    $sql = "UPDATE pc_components SET
            category_id = ?, component_name = ?, component_name_en = ?, component_name_zh = ?,
            brand = ?, model = ?, specifications = ?, base_price = ?, current_price = ?,
            stock_quantity = ?, description = ?, description_en = ?, description_zh = ?,
            is_active = ?, sort_order = ?
            WHERE id = ?";

    $stmt = execute_query($local_link, $sql, "issssssddiissiii", [
        $category_id, $component_name, $component_name_en, $component_name_zh, $brand, $model,
        $specifications, $base_price, $current_price, $stock_quantity, $description,
        $description_en, $description_zh, $is_active, $sort_order, $id
    ]);

    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'Component updated successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update component']);
    }

    close_db_connection($local_link);
}

/**
 * Delete component
 */
function deleteComponent() {
    $local_link = get_db_connection();

    $id = $_POST['id'] ?? 0;
    if (!$id) {
        echo json_encode(['success' => false, 'message' => 'Component ID required']);
        close_db_connection($local_link);
        return;
    }

    $sql = "DELETE FROM pc_components WHERE id = ?";
    $stmt = execute_query($local_link, $sql, "i", [$id]);

    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'Component deleted successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to delete component']);
    }

    close_db_connection($local_link);
}

/**
 * Get prebuilt configurations
 */
function getPrebuiltConfigs() {
    $local_link = get_db_connection();

    // Check if table exists first
    $tableCheck = mysqli_query($local_link, "SHOW TABLES LIKE 'pc_prebuilt_configs'");
    if (mysqli_num_rows($tableCheck) == 0) {
        echo json_encode(['success' => false, 'message' => 'PC components system not initialized. Please contact administrator.']);
        close_db_connection($local_link);
        return;
    }

    $tier = $_GET['tier'] ?? null;
    $primary_use = $_GET['primary_use'] ?? null;
    $active_only = $_GET['active_only'] ?? true;

    $sql = "SELECT * FROM pc_prebuilt_configs";
    $params = [];
    $types = "";
    $conditions = [];

    if ($tier) {
        $conditions[] = "tier = ?";
        $params[] = $tier;
        $types .= "s";
    }

    if ($primary_use) {
        $conditions[] = "primary_use = ?";
        $params[] = $primary_use;
        $types .= "s";
    }

    if ($active_only) {
        $conditions[] = "is_active = 1";
    }

    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }

    $sql .= " ORDER BY sort_order, tier, config_name";

    $stmt = execute_query($local_link, $sql, $types, $params);

    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $configs = [];
        while ($row = mysqli_fetch_assoc($result)) {
            // Decode JSON fields
            if ($row['components']) {
                $row['components'] = json_decode($row['components'], true);
            }
            if ($row['specifications_summary']) {
                $row['specifications_summary'] = json_decode($row['specifications_summary'], true);
            }
            $configs[] = $row;
        }
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'configs' => $configs]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch prebuilt configs']);
    }

    close_db_connection($local_link);
}

/**
 * Get single prebuilt configuration
 */
function getPrebuiltConfig() {
    $local_link = get_db_connection();

    $id = $_GET['id'] ?? 0;
    if (!$id) {
        echo json_encode(['success' => false, 'message' => 'Config ID required']);
        close_db_connection($local_link);
        return;
    }

    $sql = "SELECT * FROM pc_prebuilt_configs WHERE id = ?";
    $stmt = execute_query($local_link, $sql, "i", [$id]);

    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        if ($row = mysqli_fetch_assoc($result)) {
            // Decode JSON fields
            if ($row['components']) {
                $row['components'] = json_decode($row['components'], true);
            }
            if ($row['specifications_summary']) {
                $row['specifications_summary'] = json_decode($row['specifications_summary'], true);
            }
            echo json_encode(['success' => true, 'config' => $row]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Config not found']);
        }
        mysqli_stmt_close($stmt);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch config']);
    }

    close_db_connection($local_link);
}

// Include extended functions
require_once 'pc_components_api_extended.php';
